package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.exception.PaperGenerationException;
import com.edu.maizi_edu_sys.service.memory.MemoryManager;
import com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;

import java.util.stream.Collectors;

/**
 * 遗传算法求解器。
 * <p>
 * 该类实现了遗传算法（Genetic Algorithm, GA）的核心逻辑，用于从一个大规模的候选题目池中，
 * 根据多重目标（如总分、难度分布、题型分布、题目质量、知识点覆盖度等）优化选择，
 * 生成一份综合满足各项要求的试卷题目组合。
 * </p>
 * <p>
 * 核心流程：
 * <ol>
 *   <li><b>初始化种群</b>：随机生成一组初始解（染色体），每个染色体代表一个可能的题目组合。</li>
 *   <li><b>适应度评估</b>：为种群中的每个染色体计算适应度值。适应度函数综合考虑了各项约束指标的满足程度。</li>
 *   <li><b>遗传操作（迭代）</b>：
 *     <ul>
 *       <li><b>选择</b>：根据适应度值，通过某种选择策略（如锦标赛选择）选出优良个体作为父代。</li>
 *       <li><b>交叉</b>：父代个体以一定概率进行基因片段交换（交叉），产生新的子代染色体。</li>
 *       <li><b>变异</b>：子代染色体以一定概率发生基因突变，引入新的多样性。</li>
 *       <li>新一代种群替换旧种群。</li>
 *     </ul>
 *   </li>
 *   <li><b>终止条件</b>：达到最大迭代次数、适应度达到阈值或连续多代无显著改进时终止。</li>
 *   <li><b>结果提取</b>：返回最终种群中适应度最高的染色体所代表的题目组合。</li>
 * </ol>
 * </p>
 * <p>
 * 使用 {@link ForkJoinPool} 实现适应度评估的并行化，以提高大规模种群的处理效率。
 * 算法的各项参数（如种群大小、迭代次数、交叉率、变异率、适应度权重等）可通过 Spring Boot 的 {@code @Value} 注解从配置文件中注入，具有良好的可配置性。
 * </p>
 */
@Service
@Slf4j
public class GeneticSolver {

    // --- 线程安全相关 ---

    /** 线程本地随机数生成器，避免多线程竞争 */
    private static final ThreadLocal<Random> THREAD_LOCAL_RANDOM =
        ThreadLocal.withInitial(() -> new Random(System.nanoTime()));

    /** 并行评估的线程池 */
    private final ForkJoinPool evaluationThreadPool;



    /** 内存管理器 */
    @Autowired
    private MemoryManager memoryManager;

    /** 监控服务 */
    @Autowired
    private AlgorithmMonitoringService monitoringService;

    /** 构造函数，初始化线程池 */
    public GeneticSolver() {
        int parallelism = Math.min(Runtime.getRuntime().availableProcessors(), 4);
        this.evaluationThreadPool = new ForkJoinPool(parallelism);
    }

    // --- 遗传算法核心参数 (通过 @Value 从 application.properties 或 yaml 文件注入) ---

    /** 种群大小：每一代中包含的染色体（解方案）数量。 */
    @Value("${algorithm.genetic.population-size:100}")
    private int POPULATION_SIZE;

    /** 最大进化代数：算法迭代的最大次数。 */
    @Value("${algorithm.genetic.max-generations:200}")
    private int MAX_GENERATIONS;

    /** 最小进化代数：即使适应度已达标，也至少迭代此代数以充分搜索。 */
    @Value("${algorithm.genetic.min-generations:30}")
    private int MIN_GENERATIONS;

    /** 交叉概率：两个父代染色体进行交叉操作的概率。 */
    @Value("${algorithm.genetic.crossover-rate:0.8}")
    private double CROSSOVER_RATE;

    /** 基础变异概率：单个基因位发生突变的概率。算法内部可能会动态调整此概率。 */
    @Value("${algorithm.genetic.mutation-rate:0.1}")
    private double MUTATION_RATE;

    /** 锦标赛选择中参与竞争的个体数量。 */
    @Value("${algorithm.genetic.tournament-size:5}")
    private int TOURNAMENT_SIZE;

    // --- 适应度函数各组成部分的权重 ---

    /** 总分匹配度权重：衡量选出题目总分与目标总分接近程度的重要性。 */
    @Value("${algorithm.genetic.fitness-weights.score:0.4}")
    private double WEIGHT_SCORE;

    /** 题目质量权重：衡量选中题目平均质量的重要性（例如基于题目使用频率、专家评分等）。 */
    @Value("${algorithm.genetic.fitness-weights.quality:0.2}")
    private double WEIGHT_QUALITY;

    /** 难度分布匹配度权重：衡量试卷整体难度分布与目标分布的吻合程度。 */
    @Value("${algorithm.genetic.fitness-weights.difficulty-distribution:0.15}")
    private double WEIGHT_DIFFICULTY_DIST;

    /** 认知层次分布匹配度权重：衡量题目在不同认知层次（如记忆、理解、应用）上的分布与目标是否一致。 */
    @Value("${algorithm.genetic.fitness-weights.cognitive-distribution:0.15}")
    private double WEIGHT_COGNITIVE_DIST;

    /** 知识点覆盖度权重：衡量试卷对目标知识点的覆盖程度。 */
    @Value("${algorithm.genetic.fitness-weights.kp-coverage:0.05}")
    private double WEIGHT_KP_COVERAGE;

    /** 题型覆盖度权重：衡量题目选择对不同题型的覆盖度，优先保证题型多样性。 */
    @Value("${algorithm.genetic.fitness-weights.topic-type-diversity:0.05}")
    private double WEIGHT_TOPIC_TYPE_DIVERSITY;

    /**  知识点题型均衡权重：衡量每个知识点内部题型分布的均匀性，避免某个知识点某题型过多而某题型过少。 */
    @Value("${algorithm.genetic.fitness-weights.kp-type-balance:0.1}")
    private double WEIGHT_KP_TYPE_BALANCE;

    /** 标签多样性权重：衡量题目标签（细分知识点）覆盖的丰富度，避免集中在少数标签 */
    @Value("${algorithm.genetic.fitness-weights.tag-diversity:0.05}")
    private double WEIGHT_TAG_DIVERSITY;

    // --- 难度区间阈值（可配置） ---
    @Value("${algorithm.genetic.difficulty.easy-max:0.4}")
    private double DIFF_EASY_MAX;
    @Value("${algorithm.genetic.difficulty.medium-min:0.5}")
    private double DIFF_MEDIUM_MIN;
    @Value("${algorithm.genetic.difficulty.medium-max:0.7}")
    private double DIFF_MEDIUM_MAX;
    @Value("${algorithm.genetic.difficulty.hard-min:0.8}")
    private double DIFF_HARD_MIN;

    /** 提前终止阈值：当最佳适应度达到此值时，且已超过最小迭代次数，可提前结束进化。 */
    @Value("${algorithm.genetic.early-terminate-threshold:0.97}")
    private double EARLY_TERMINATE_THRESHOLD;

    /** 是否启用硬约束模式：一旦题型或分数不符即将适应度置0 */
    @Value("${algorithm.genetic.hard-constraints:true}")
    private boolean HARD_CONSTRAINT_MODE;

    // --- Recency Penalty Configuration (New) ---
    @Value("${algorithm.genetic.recency.very-recent-days:7}")
    private int VERY_RECENT_DAYS_THRESHOLD;
    @Value("${algorithm.genetic.recency.recent-days:30}")
    private int RECENT_DAYS_THRESHOLD;
    @Value("${algorithm.genetic.recency.less-recent-days:90}")
    private int LESS_RECENT_DAYS_THRESHOLD;

    @Value("${algorithm.genetic.recency.penalty-very-recent:0.6}")
    private double PENALTY_VERY_RECENT;
    @Value("${algorithm.genetic.recency.penalty-recent:0.3}")
    private double PENALTY_RECENT;
    @Value("${algorithm.genetic.recency.penalty-less-recent:0.1}")
    private double PENALTY_LESS_RECENT;
    @Value("${algorithm.genetic.quality.min-topic-quality:0.05}") // Minimum quality score for a topic
    private double MIN_TOPIC_QUALITY;

    /** 全局超时时间（秒） */
    @Value("${algorithm.genetic.global-timeout-seconds:2}")
    private int GLOBAL_TIMEOUT_SECONDS;

    /** 自适应变异是否启用 */
    @Value("${algorithm.genetic.adaptive-mutation.enabled:true}")
    private boolean ADAPTIVE_MUTATION_ENABLED;

    /** 自适应变异最大率 */
    @Value("${algorithm.genetic.adaptive-mutation.max-rate:0.3}")
    private double ADAPTIVE_MUTATION_MAX_RATE;

    /** 自适应变异最小率 */
    @Value("${algorithm.genetic.adaptive-mutation.min-rate:0.05}")
    private double ADAPTIVE_MUTATION_MIN_RATE;

    /** 自适应变异停滞阈值 */
    @Value("${algorithm.genetic.adaptive-mutation.stagnation-threshold:5}")
    private int ADAPTIVE_MUTATION_STAGNATION_THRESHOLD;

    // 注入新组件
    @Autowired
    private RepairOperator repairOperator;

    @Autowired
    private FeasibilityChecker feasibilityChecker;

    @Autowired
    private GreedySeedGenerator greedySeedGenerator;

    @Autowired
    private KnowledgePointConstraintChecker knowledgePointConstraintChecker;

    /**
     * 获取线程安全的随机数生成器
     */
    private Random getThreadSafeRandom() {
        return THREAD_LOCAL_RANDOM.get();
    }

    /**
     * 执行遗传算法，从候选题目中选取最优组合以构成试卷。
     *
     * @param availableQuestions          候选题目列表，经过初步筛选和过滤。
     * @param targetScore                 试卷的目标总分。
     * @param typeTargetCounts            (未使用，但保留参数以兼容) 目标题型数量，用于优化每种题型的选取数量
     * @param difficultyDistributionTarget 目标难度分布，Map<难度名称, 期望百分比>。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布，Map<认知层次名称, 期望百分比>。
     * @param enhancementDataMap          题目增强数据，Map<题目ID, TopicEnhancementData>，包含使用次数、认知层次等。
     * @param targetKnowledgeIds          目标知识点ID列表，用于评估知识点覆盖度。
     * @return 经过遗传算法优化选出的题目列表。若无法找到合适解，则可能返回空列表。
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts, // 目标题型数量，用于优化每种题型的选取数量
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds) {

        return solve(availableQuestions, targetScore, typeTargetCounts, difficultyDistributionTarget,
                    cognitiveLevelDistributionTarget, enhancementDataMap, targetKnowledgeIds, null);
    }

    /**
     * 执行遗传算法，支持知识点级别约束的版本
     *
     * @param availableQuestions          候选题目列表，经过初步筛选和过滤。
     * @param targetScore                 试卷的目标总分。
     * @param typeTargetCounts            目标题型数量，用于优化每种题型的选取数量
     * @param difficultyDistributionTarget 目标难度分布，Map<难度名称, 期望百分比>。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布，Map<认知层次名称, 期望百分比>。
     * @param enhancementDataMap          题目增强数据，Map<题目ID, TopicEnhancementData>，包含使用次数、认知层次等。
     * @param targetKnowledgeIds          目标知识点ID列表，用于评估知识点覆盖度。
     * @param knowledgePointConfigs       知识点配置列表，用于知识点级别约束检查
     * @return 经过遗传算法优化选出的题目列表。若无法找到合适解，则可能返回空列表。
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts,
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds,
                             List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs) {

        return solve(availableQuestions, targetScore, typeTargetCounts, difficultyDistributionTarget,
                    cognitiveLevelDistributionTarget, enhancementDataMap, targetKnowledgeIds,
                    knowledgePointConfigs, null);
    }

    /**
     * 执行遗传算法，支持知识点级别约束和题型分值配置的版本
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts,
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds,
                             List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                             Map<String, Integer> typeScoreMap) {

        if (availableQuestions == null || availableQuestions.isEmpty()) {
            log.warn("GeneticSolver: Available questions list is null or empty. Cannot solve.");
            return Collections.emptyList();
        }



        // 开始监控
        String requestId = "GA_" + System.currentTimeMillis();
        AlgorithmMonitoringService.ExecutionContext context = monitoringService.startExecution(requestId);

        try {
            // 检查内存状态
            if (memoryManager.isMemoryPressure()) {
                log.warn("High memory pressure detected before algorithm execution");
                memoryManager.checkGarbageCollection();
            }

            List<Topic> result = solveInternal(availableQuestions, targetScore, typeTargetCounts,
                               difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                               enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap);

            // 记录成功执行
            double bestFitness = result.isEmpty() ? 0.0 : 1.0; // 简化的适应度记录
            monitoringService.recordSuccess(context, bestFitness, MAX_GENERATIONS);

            return result;

        } catch (PaperGenerationException e) {
            monitoringService.recordFailure(context, e.getErrorCode().name(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in genetic algorithm: {}", e.getMessage(), e);
            monitoringService.recordFailure(context, "UNEXPECTED_ERROR", e.getMessage());
            throw new PaperGenerationException(
                PaperGenerationException.ErrorCode.ALGORITHM_TIMEOUT,
                "算法执行过程中发生未预期错误: " + e.getMessage(),
                null, e
            );
        }
    }

    /**
     * 内部求解方法，实际执行遗传算法逻辑
     */
    private List<Topic> solveInternal(List<Topic> availableQuestions,
                                    int targetScore,
                                    Map<String, Integer> typeTargetCounts,
                                    Map<String, Double> difficultyDistributionTarget,
                                    Map<String, Double> cognitiveLevelDistributionTarget,
                                    Map<Integer, TopicEnhancementData> enhancementDataMap,
                                    List<Integer> targetKnowledgeIds,
                                    List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                                    Map<String, Integer> typeScoreMap) {

        long startTime = System.currentTimeMillis();
        log.info("GeneticSolver started. Population: {}, Max Generations: {}, Crossover: {}, Mutation: {}, Tournament: {}",
                 POPULATION_SIZE, MAX_GENERATIONS, CROSSOVER_RATE, MUTATION_RATE, TOURNAMENT_SIZE);
        log.info("Fitness Weights: Score={}, Quality={}, DifficultyDist={}, CognitiveDist={}, KPCoverage={}, TypeDiversity={}, KPTypeBalance={}",
                 WEIGHT_SCORE, WEIGHT_QUALITY, WEIGHT_DIFFICULTY_DIST, WEIGHT_COGNITIVE_DIST, WEIGHT_KP_COVERAGE,
                 WEIGHT_TOPIC_TYPE_DIVERSITY, WEIGHT_KP_TYPE_BALANCE);

        // 按类型分组待选题目，用于初始化和其它操作
        Map<String, List<Topic>> topicsByType = new HashMap<>();
        for (Topic topic : availableQuestions) {
            String type = getStandardTopicType(topic.getType());
            topicsByType.computeIfAbsent(type, k -> new ArrayList<>()).add(topic);
        }

        // 预检查：验证候选题库中是否有足够每种类型的题目
        boolean hasEnoughTopicsOfAllTypes = true;
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int requiredCount = entry.getValue();
            int availableCount = topicsByType.getOrDefault(getStandardTopicType(type), Collections.emptyList()).size();

            if (requiredCount > 0 && availableCount < requiredCount) {
                log.warn("题型 {} 可用题目不足，需要 {} 题，但只有 {} 题可用",
                       type, requiredCount, availableCount);
                hasEnoughTopicsOfAllTypes = false;
            }
        }

        if (!hasEnoughTopicsOfAllTypes) {
            log.warn("题库中存在题型数量不足的情况，遗传算法将无法完全满足题型要求");
        }

        // 1. 初始化种群 - 使用改进的混合初始化方法
        List<Chromosome> population = new ArrayList<>(POPULATION_SIZE);

        // 1.1 生成启发式种子（5-10个高质量种子）
        int seedCount = Math.min(10, POPULATION_SIZE / 10);
        if (seedCount > 0) {
            List<Chromosome> seeds = greedySeedGenerator.generateSeeds(
                availableQuestions, typeTargetCounts, targetScore,
                difficultyDistributionTarget, null, enhancementDataMap, seedCount
            );
            population.addAll(seeds);
            log.info("Generated {} greedy seeds for initial population", seeds.size());
        }

        // 1.2 尝试强制类型匹配初始化填充剩余种群
        if (population.size() < POPULATION_SIZE) {
            List<Chromosome> typeMatchedPopulation = enforceExactTypeCountsInitialization(
                availableQuestions, typeTargetCounts
            );

            // 只添加需要的数量
            int needed = POPULATION_SIZE - population.size();
            int toAdd = Math.min(needed, typeMatchedPopulation.size());
            for (int i = 0; i < toAdd; i++) {
                population.add(typeMatchedPopulation.get(i));
            }
        }

        // 1.3 如果仍然不足，用随机初始化补充
        if (population.size() < POPULATION_SIZE) {
            int remaining = POPULATION_SIZE - population.size();
            List<Chromosome> randomPopulation = initializePopulation(availableQuestions.size());

            // 只添加需要的数量
            int toAdd = Math.min(remaining, randomPopulation.size());
            for (int i = 0; i < toAdd; i++) {
                population.add(randomPopulation.get(i));
            }
            log.warn("Added {} random chromosomes due to insufficient type-matched initialization", toAdd);
        }

        // 1.4 对初始种群应用修复算子（确保硬约束）
        if (repairOperator != null) {
            for (Chromosome ch : population) {
                repairOperator.repairChromosome(ch, availableQuestions, typeTargetCounts,
                        targetScore, null, enhancementDataMap);
            }
            log.debug("Applied repair operator to initial population of size {}", population.size());
        }

        // 2. 评估初始种群的适应度
        evaluatePopulation(population, availableQuestions, targetScore,
                          typeTargetCounts, // Added
                          difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                          enhancementDataMap, targetKnowledgeIds,
                          knowledgePointConfigs, // Added
                          typeScoreMap);

        // 3. 遗传算法主循环
        double bestFitnessOverall = 0.0; // 记录整个进化过程中的历史最佳适应度
        int generationsWithoutImprovement = 0; // 记录连续未改进的代数
        long algorithmStartTime = System.currentTimeMillis(); // 全局超时控制

        // 准备知识点配置用于可行性检查（暂时简化）
        List<FeasibilityChecker.KnowledgePointConfig> kpConfigs = new ArrayList<>();

        for (int generation = 0; generation < MAX_GENERATIONS; generation++) {
            // 全局超时检查
            if (System.currentTimeMillis() - algorithmStartTime > GLOBAL_TIMEOUT_SECONDS * 1000L) {
                log.warn("Global timeout reached after {}ms, terminating at generation {}",
                        System.currentTimeMillis() - algorithmStartTime, generation);
                break;
            }
            // a. 创建新一代种群列表
            List<Chromosome> newPopulation = new ArrayList<>(POPULATION_SIZE);

            // b. 精英主义选择：直接将当前种群中适应度最高的个体复制到下一代，确保最优解不丢失
            if (!population.isEmpty()) {
                 newPopulation.add(population.get(0)); // population已按适应度降序排列
            }

            // 自适应变异率计算
            double dynamicMutationRate = calculateAdaptiveMutationRate(generation, generationsWithoutImprovement);

            // c. 通过选择、交叉和变异生成新个体，填充新种群至POPULATION_SIZE
            while (newPopulation.size() < POPULATION_SIZE) {
                Chromosome parent1 = tournamentSelection(population); // 通过锦标赛选择父代1
                Chromosome parent2 = tournamentSelection(population); // 通过锦标赛选择父代2

                // 使用类型保持交叉操作
                Chromosome child = typePreservingCrossover(parent1, parent2, availableQuestions, typeTargetCounts);

                // 以动态变异率执行类型保持变异操作
                if (getThreadSafeRandom().nextDouble() < dynamicMutationRate) {
                    typePreservingMutate(child, availableQuestions, typeTargetCounts);
                }

                // 应用修复算子
                if (repairOperator != null) {
                    int repairSteps = repairOperator.repairChromosome(
                        child, availableQuestions, typeTargetCounts,
                        targetScore, null, enhancementDataMap
                    );
                    if (repairSteps > 0) {
                        log.debug("Applied {} repair steps to chromosome", repairSteps);
                    }
                }

                newPopulation.add(child);
            }

            // d. 评估新一代种群的适应度
            evaluatePopulation(newPopulation, availableQuestions, targetScore,
                              typeTargetCounts, // Added
                              difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                              enhancementDataMap, targetKnowledgeIds,
                              knowledgePointConfigs, // Added
                              typeScoreMap);

            // e. 新种群替换旧种群
            population = newPopulation;

            // f. 检查并更新全局最优适应度，并处理提前终止条件
            double currentBestFitnessInGeneration = population.get(0).getFitness(); // 当前代最佳适应度
            if (currentBestFitnessInGeneration > bestFitnessOverall) {
                bestFitnessOverall = currentBestFitnessInGeneration;
                generationsWithoutImprovement = 0; // 重置未改进计数器
            } else {
                generationsWithoutImprovement++;
            }

            // 记录日志，方便追踪进化过程
            if (generation % 10 == 0 || generation == MAX_GENERATIONS - 1) {
                // 检查当前最佳解的题型分布
                Chromosome bestChromosome = population.get(0);
                Map<String, Integer> typeCounts = getTypeDistribution(bestChromosome, availableQuestions);

                log.info("Generation {}: Best Fitness = {:.4f}, Score = {}, Type Distribution = {}",
                        generation, currentBestFitnessInGeneration, bestChromosome.getTotalScore(), typeCounts);

                // 检查题型分布是否满足要求
                boolean allTypesSatisfied = true;
                for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                    String type = entry.getKey();
                    int required = entry.getValue();
                    int actual = typeCounts.getOrDefault(type, 0);

                    if (required > 0 && actual != required) {
                        allTypesSatisfied = false;
                        log.warn("Type constraint not met: {} (required={}, actual={})",
                                type, required, actual);
                    }
                }

                if (allTypesSatisfied) {
                    log.info("All type constraints satisfied at generation {}", generation);
                }
            }

            // 可行解检测和提前终止条件判断
            Chromosome bestChromosome = population.isEmpty() ? null : population.get(0);
            boolean isFeasible = false;
            boolean kpConstraintsSatisfied = false;

            if (bestChromosome != null && feasibilityChecker != null) {
                FeasibilityChecker.FeasibilityResult feasibilityResult = feasibilityChecker.checkFeasibility(
                    bestChromosome, availableQuestions, typeTargetCounts,
                    targetScore, null, kpConfigs
                );
                isFeasible = feasibilityResult.isFeasible();

                // 检查知识点级别约束
                if (isFeasible && knowledgePointConfigs != null && knowledgePointConstraintChecker != null) {
                    List<Topic> selectedTopics = getSelectedTopics(bestChromosome, availableQuestions);
                    KnowledgePointConstraintChecker.KnowledgePointConstraintResult kpResult =
                        knowledgePointConstraintChecker.checkConstraints(selectedTopics, knowledgePointConfigs);
                    kpConstraintsSatisfied = kpResult.isValid();

                    if (!kpConstraintsSatisfied) {
                        log.debug("Knowledge point constraints not satisfied at generation {}: {}",
                                generation, kpResult.getMessage());
                    }
                }

                if (isFeasible && (knowledgePointConfigs == null || kpConstraintsSatisfied) && generation >= MIN_GENERATIONS) {
                    log.info("Feasible solution with satisfied KP constraints found at generation {}. Terminating early.", generation);
                    break;
                }
            }

            // 传统提前终止条件判断：
            // 1. 必须已完成最小进化代数。
            // 2. 且 (全局最佳适应度已达到预设的优良阈值 或 连续多代适应度没有显著提升)
            if (generation >= MIN_GENERATIONS &&
                (bestFitnessOverall >= EARLY_TERMINATE_THRESHOLD || generationsWithoutImprovement >= 10)) {
                log.info("Early termination triggered at generation {}. Overall Best Fitness: {:.4f}. Generations without improvement: {}.",
                         generation, bestFitnessOverall, generationsWithoutImprovement);
                break;
            }
        }

        // 4. 从最终种群中提取最佳染色体（适应度最高的那个）
        Chromosome bestSolutionChromosome = population.isEmpty() ? null : population.get(0);
        List<Topic> result = Collections.emptyList();
        if (bestSolutionChromosome != null) {
            result = extractSolution(bestSolutionChromosome, availableQuestions);

            // 验证最终结果是否满足题型数量要求
            Map<String, Integer> resultTypeCounts = new HashMap<>();
            for (Topic topic : result) {
                String type = getStandardTopicType(topic.getType());
                resultTypeCounts.put(type, resultTypeCounts.getOrDefault(type, 0) + 1);
            }

            boolean allTypesSatisfied = true;
            for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                String type = entry.getKey();
                int required = entry.getValue();
                int actual = resultTypeCounts.getOrDefault(type, 0);

                if (required > 0 && actual != required) {
                    allTypesSatisfied = false;
                    log.warn("Final solution type constraint not met: {} (required={}, actual={})",
                            type, required, actual);
                }
            }

            if (allTypesSatisfied) {
                log.info("Final solution satisfies all type constraints");
            } else {
                log.warn("Final solution does not satisfy all type constraints. This should not happen with type-preserving operators.");
            }

            log.info("Genetic algorithm completed in {}ms. Best solution fitness: {:.4f}, Selected {} topics with total score: {}.",
                (System.currentTimeMillis() - startTime), bestSolutionChromosome.getFitness(), result.size(), bestSolutionChromosome.getTotalScore());
        } else {
            log.warn("Genetic algorithm finished but population was empty. No solution found. Time: {}ms", (System.currentTimeMillis() - startTime));
        }

        return result;
    }

    /**
     * 获取染色体代表的题目组合的题型分布
     */
    private Map<String, Integer> getTypeDistribution(Chromosome chromosome, List<Topic> questions) {
        Map<String, Integer> typeCounts = new HashMap<>();
        BitSet gene = chromosome.getGene();

        for (int i = 0; i < questions.size(); i++) {
            if (gene.get(i)) {
                Topic topic = questions.get(i);
                String type = getStandardTopicType(topic.getType());
                typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
            }
        }

        return typeCounts;
    }

    /**
     * 强制类型数量匹配的初始化方法
     * 生成的每个染色体都确保精确包含目标数量的每种题型
     */
    private List<Chromosome> enforceExactTypeCountsInitialization(List<Topic> availableQuestions, Map<String, Integer> typeTargetCounts) {
        List<Chromosome> population = new ArrayList<>(POPULATION_SIZE);
        if (typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            // 如果没有题型限制，回退到标准初始化
            return initializePopulation(availableQuestions.size());
        }

        // 按题型分组所有题目
        Map<String, List<Integer>> indexesByType = new HashMap<>();
        for (int i = 0; i < availableQuestions.size(); i++) {
            Topic topic = availableQuestions.get(i);
            String type = getStandardTopicType(topic.getType());
            indexesByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 调试：输出各题型的可用题目数量
        log.info("题库中各题型的可用题目数量:");
        for (Map.Entry<String, List<Integer>> entry : indexesByType.entrySet()) {
            log.info("- {}: {} 题", entry.getKey(), entry.getValue().size());
        }

        // 检查是否有足够的每种类型题目
        boolean hasSufficientTopics = true;
        log.info("检查题型约束要求:");
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int requiredCount = entry.getValue();
            List<Integer> availableIndexes = indexesByType.getOrDefault(type, Collections.emptyList());

            log.info("- 题型 {}: 需要 {} 题，可用 {} 题", type, requiredCount, availableIndexes.size());

            if (requiredCount > 0 && availableIndexes.size() < requiredCount) {
                log.warn("题型 {} 题目不足，需要 {} 题，但只有 {} 题可用",
                       type, requiredCount, availableIndexes.size());
                hasSufficientTopics = false;
            } else if (requiredCount > 0) {
                log.info("题型 {} 题目充足", type);
            }
        }

        if (!hasSufficientTopics) {
            log.warn("无法创建满足题型数量要求的初始种群");
            return Collections.emptyList();
        }

        Random random = getThreadSafeRandom();

        // 生成POPULATION_SIZE个染色体，每个都严格满足题型数量要求
        for (int i = 0; i < POPULATION_SIZE; i++) {
            BitSet gene = new BitSet(availableQuestions.size());

            // 确保每种题型都选择精确的目标数量
            for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                String type = entry.getKey();
                int targetCount = entry.getValue();
                List<Integer> availableIndices = new ArrayList<>(indexesByType.getOrDefault(type, Collections.emptyList()));

                if (targetCount > 0 && !availableIndices.isEmpty()) {
                    // 随机打乱可用索引
                    Collections.shuffle(availableIndices, random);

                    // 选择所需数量的题目索引
                    for (int j = 0; j < Math.min(targetCount, availableIndices.size()); j++) {
                        gene.set(availableIndices.get(j));
                    }
                }
            }

            // 添加染色体到种群
            population.add(new Chromosome(gene));
        }

        log.info("成功创建了 {} 个染色体的强制类型匹配初始种群", population.size());
        return population;
    }

    /**
     * 计算给定实际计数的分布与目标分布之间的适应度。
     * 适应度越高，表示实际分布与目标分布越吻合。
     * 计算方法是基于各项的百分比偏差之和的倒数。
     *
     * @param actualCounts         实际各项的计数 (例如 Map<难度名称, 数量> 或 Map<认知层次名称, 数量>)
     * @param targetDistribution   目标各项的百分比分布 (例如 Map<难度名称, 期望百分比>)
     * @param totalItems           实际选中的总项目数，用于计算各项的实际百分比。
     * @return 分布适应度值，介于0和1之间，1表示完美匹配。
     */
    private double calculateDistributionFitness(Map<String, Integer> actualCounts,
                                              Map<String, Double> targetDistribution,
                                              int totalItems) {
        if (targetDistribution == null || targetDistribution.isEmpty() || totalItems == 0) {
            return 1.0; // 没有目标分布或没有选中题目，视为完美匹配或不参与评估
        }

        double totalDeviation = 0.0;
        // 遍历所有目标分布项 (如 easy, medium, hard)
        for (Map.Entry<String, Double> targetEntry : targetDistribution.entrySet()) {
            String key = targetEntry.getKey(); // 难度/认知层次的名称
            double targetPercent = targetEntry.getValue(); // 期望的百分比
            // 计算实际百分比：该项的实际数量 / 总数量
            double actualPercent = (double) actualCounts.getOrDefault(key, 0) / totalItems;
            // 累加绝对偏差
            totalDeviation += Math.abs(targetPercent - actualPercent);
        }

        // 适应度公式：1 / (1 + 总偏差)。总偏差为0时，适应度为1。
        return 1.0 / (1.0 + totalDeviation);
    }

    /**
     * 并行评估整个种群中所有染色体的适应度。
     * 使用自定义的 {@link ForkJoinPool} 来控制并行度，避免过多消耗系统资源。
     * 评估完成后，种群将按照适应度从高到低排序。
     *
     * @param population                     待评估的染色体种群。
     * @param questions                      题库中所有可用题目列表。
     * @param targetScore                    试卷目标总分。
     * @param difficultyDistributionTarget   目标难度分布。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布。
     * @param enhancementDataMap             题目增强数据。
     * @param targetKnowledgeIds             目标知识点ID列表。
     */
    private void evaluatePopulation(List<Chromosome> population,
                                List<Topic> questions,
                                int targetScore,
                                Map<String, Integer> typeTargetCounts, // Added
                                Map<String, Double> difficultyDistributionTarget,
                                Map<String, Double> cognitiveLevelDistributionTarget,
                                Map<Integer, TopicEnhancementData> enhancementDataMap,
                                List<Integer> targetKnowledgeIds,
                                List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs, // Added
                                Map<String, Integer> typeScoreMap) {
        if (population == null || population.isEmpty()) return;

        try {
            // 使用自定义线程池进行并行评估，避免双重并行
            List<java.util.concurrent.Future<Void>> futures = new ArrayList<>();

            for (Chromosome chromosome : population) {
                java.util.concurrent.Future<Void> future = evaluationThreadPool.submit(() -> {
                    evaluateChromosome(
                        chromosome,
                        questions,
                        targetScore,
                        typeTargetCounts, // Added
                        difficultyDistributionTarget,
                        cognitiveLevelDistributionTarget,
                        enhancementDataMap,
                        targetKnowledgeIds,
                        knowledgePointConfigs, // Added
                        typeScoreMap
                    );
                    return null;
                });
                futures.add(future);
            }

            // 等待所有任务完成
            for (java.util.concurrent.Future<Void> future : futures) {
                future.get();
            }

        } catch (Exception e) {
            log.error("Error during parallel population evaluation: {}. Falling back to sequential evaluation.", e.getMessage(), e);
            // 如果并行评估出错，则回退到串行评估模式，确保程序健壮性
            for (Chromosome chromosome : population) {
                evaluateChromosome(
                    chromosome,
                    questions,
                    targetScore,
                    typeTargetCounts, // Added
                    difficultyDistributionTarget,
                    cognitiveLevelDistributionTarget,
                    enhancementDataMap,
                    targetKnowledgeIds,
                    knowledgePointConfigs, // Added
                    typeScoreMap
                );
            }
        }
        // 评估完成后，按适应度从高到低（降序）对种群进行排序
        population.sort(Comparator.comparing(Chromosome::getFitness).reversed());
    }

    /**
     * 评估单个染色体的适应度。
     * 适应度是多个子适应度（总分、质量、难度分布、认知层次分布、知识点覆盖）的加权和。
     * 每个子适应度衡量染色体（即题目组合）在对应维度上与目标的吻合程度。
     *
     * @param chromosome                    待评估的染色体。
     * @param questions                     题库中所有可用题目列表。
     * @param targetScore                   试卷目标总分。
     * @param difficultyDistributionTarget  目标难度分布。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布。
     * @param enhancementDataMap            题目增强数据。
     * @param targetKnowledgeIds            目标知识点ID列表。
     */
    @SuppressWarnings("deprecation") // 如果 Topic.getScore() 可能返回 null 且有特定处理逻辑，可保留。否则考虑移除。
    /**
     * Evaluate chromosome with optional hard-constraint mode: any violation yields fitness 0.
     */
    private void evaluateChromosome(Chromosome chromosome,
                                 List<Topic> questions,
                                 int targetScore,
                                 Map<String, Integer> typeTargetCounts, // Added
                                 Map<String, Double> difficultyDistributionTarget,
                                 Map<String, Double> cognitiveLevelDistributionTarget,
                                 Map<Integer, TopicEnhancementData> enhancementDataMap,
                                 List<Integer> targetKnowledgeIds,
                                 List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs, // Added
                                 Map<String, Integer> typeScoreMap) {
        // --- Hard-constraint quick rejection (types & score) ---
        if (HARD_CONSTRAINT_MODE && typeTargetCounts != null && !typeTargetCounts.isEmpty()) {
            Map<String, Integer> countsTmp = new java.util.HashMap<>();
            BitSet g = chromosome.getGene();
            for (int idx = g.nextSetBit(0); idx >= 0 && idx < questions.size(); idx = g.nextSetBit(idx + 1)) {
                String t = getStandardTopicType(questions.get(idx).getType());
                countsTmp.put(t, countsTmp.getOrDefault(t, 0) + 1);
            }
            int hardCurrentScore = 0;
            BitSet g2 = chromosome.getGene();
            for (int idx = g2.nextSetBit(0); idx >= 0 && idx < questions.size(); idx = g2.nextSetBit(idx + 1)) {
                hardCurrentScore += typeScoreMap != null && !typeScoreMap.isEmpty()
                        ? typeScoreMap.getOrDefault(convertDbTypeToFrontendType(getStandardTopicType(questions.get(idx).getType())),
                                questions.get(idx).getScore() != null ? questions.get(idx).getScore() : 0)
                        : (questions.get(idx).getScore() != null ? questions.get(idx).getScore() : 0);
            }
            if (hardCurrentScore != targetScore) {
                chromosome.setFitness(0.0);
                return;
            }
            for (Map.Entry<String, Integer> e : typeTargetCounts.entrySet()) {
                if (countsTmp.getOrDefault(e.getKey(), 0) != e.getValue()) {
                    chromosome.setFitness(0.0);
                    return;
                }
            }
        }

        List<Topic> selectedQuestions = new ArrayList<>();
        BitSet gene = chromosome.getGene(); // 获取染色体的基因序列 (BitSet)

        // --- 1. 计算染色体代表的题目组合的总分和题目列表 ---
        int currentTotalScore = 0;
        for (int i = 0; i < gene.length(); i++) { // 遍历基因位
            if (i < questions.size() && gene.get(i)) { // 如果该基因位为1 (选中) 且不越界
                Topic question = questions.get(i);
                selectedQuestions.add(question);

                // 使用typeScoreMap计算分数，如果没有配置则使用题目本身的分数
                int questionScore = 0;
                if (typeScoreMap != null && !typeScoreMap.isEmpty()) {
                    String topicType = getStandardTopicType(question.getType());
                    // 将数据库格式转换为前端格式进行查找
                    String frontendType = convertDbTypeToFrontendType(topicType);
                    questionScore = typeScoreMap.getOrDefault(frontendType,
                                   question.getScore() != null ? question.getScore() : 0);
                } else {
                    questionScore = question.getScore() != null ? question.getScore() : 0;
                }

                currentTotalScore += questionScore;
            }
        }
        chromosome.setTotalScore(currentTotalScore); // 记录该染色体的总分

        int totalSelectedTopics = selectedQuestions.size();
        if (totalSelectedTopics == 0) { // 如果没有选中任何题目，则适应度为0，避免后续除零等问题
            chromosome.setFitness(0.0);
            return;
        }

        // --- 2. 计算分数适应度 (Score Fitness) ---
        // 目标是总分尽可能接近 targetScore。
        // 公式：1.0 / (1.0 + |目标分数 - 当前分数| / 目标分数)。分差越小，适应度越高，最大为1。
        // Ensure targetScore is not zero to avoid division by zero if currentTotalScore is also zero.
        double scoreFitness = 1.0;
        if (HARD_CONSTRAINT_MODE && targetScore > 0 && Math.abs(targetScore - currentTotalScore) > 0) {
            chromosome.setFitness(0.0);
            return;
        }

        if (targetScore > 0) {
            scoreFitness = 1.0 / (1.0 + (double)Math.abs(targetScore - currentTotalScore) / targetScore);
        } else if (currentTotalScore != 0) { // target is 0, but current is not, penalize
            scoreFitness = 0.0;
        } // If targetScore is 0 and currentTotalScore is 0, scoreFitness remains 1.0 (perfect match)

        // --- 3. 计算题目质量适应度 (Quality Fitness) ---
        // 衡量所选题目集合的平均"质量"。
        // 质量综合考虑使用次数和上次使用时间。
        double averageQuality = 0.5; // Default medium quality
        if (!selectedQuestions.isEmpty()) { // Ensure there are questions to calculate quality for
            double totalTopicSpecificQuality = 0.0;
            for (Topic question : selectedQuestions) {
                TopicEnhancementData data = enhancementDataMap != null ? enhancementDataMap.get(question.getId()) : null;

                double usagePenalty = 0.0;
                double recencyPenalty = 0.0;
                double baseQualityForTopic = 1.0; // Max quality for a topic before penalties

                if (data != null) {
                    // Usage Count Penalty: 1 - (1 / (1 + usageCount))
                    // Lower usage count means lower penalty (closer to 0)
                    if (data.getUsageCount() != null && data.getUsageCount() > 0) {
                        usagePenalty = 1.0 - (1.0 / (1.0 + data.getUsageCount()));
                    }

                    // Recency Penalty
                    if (data.getLastUsedTime() != null) {
                        long daysSinceLastUse = java.time.temporal.ChronoUnit.DAYS.between(data.getLastUsedTime(), java.time.LocalDateTime.now());

                        if (daysSinceLastUse <= VERY_RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_VERY_RECENT;
                        } else if (daysSinceLastUse <= RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_RECENT;
                        } else if (daysSinceLastUse <= LESS_RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_LESS_RECENT;
                        }
                    }
                }
                // If no enhancement data, usagePenalty and recencyPenalty remain 0.

                // Combine: Start with baseQuality and subtract penalties.
                double topicQuality = baseQualityForTopic - usagePenalty - recencyPenalty;
                totalTopicSpecificQuality += Math.max(MIN_TOPIC_QUALITY, topicQuality); // Ensure a small minimum quality
            }
            averageQuality = totalTopicSpecificQuality / totalSelectedTopics; // average of topic specific qualities
        }

        // --- 4. 计算难度分布适应度 (Difficulty Distribution Fitness) ---
        // 衡量所选题目在不同难度级别（easy, medium, hard）上的数量分布与目标分布的吻合程度。
        Map<String, Integer> actualDifficultyCounts = new HashMap<>();
        for (Topic question : selectedQuestions) {
            String difficultyName = getDifficultyName(question.getDifficulty()); // 将数值难度映射为名称
            actualDifficultyCounts.put(difficultyName, actualDifficultyCounts.getOrDefault(difficultyName, 0) + 1);
        }
        double difficultyDistFitness = calculateNumericDistributionFitness(
                selectedQuestions.stream().collect(Collectors.toMap(Topic::getDifficulty, q -> 1, Integer::sum)), // 使用原始数值难度计数
                difficultyDistributionTarget,
                totalSelectedTopics
        );

        // --- 5. 计算认知层次分布适应度 (Cognitive Level Distribution Fitness) ---
        // 衡量题目在不同认知层次上的数量分布与目标分布的吻合度。
        double cognitiveDistFitness = 1.0; // 默认完美匹配，如果无相关数据或目标
        if (cognitiveLevelDistributionTarget != null && !cognitiveLevelDistributionTarget.isEmpty() &&
            WEIGHT_COGNITIVE_DIST > 0.001 && // Only calculate if weight is meaningful
            enhancementDataMap != null && !enhancementDataMap.isEmpty()) {
            Map<String, Integer> actualCognitiveCounts = new HashMap<>();
            int topicsWithCognitiveData = 0;
            for (Topic question : selectedQuestions) {
                TopicEnhancementData data = enhancementDataMap.get(question.getId());
                if (data != null && data.getCognitiveLevel() != null && !data.getCognitiveLevel().trim().isEmpty()) {
                    actualCognitiveCounts.put(data.getCognitiveLevel(),
                            actualCognitiveCounts.getOrDefault(data.getCognitiveLevel(), 0) + 1);
                    topicsWithCognitiveData++;
                }
            }
            // Only calculate if there are topics with cognitive data and a target distribution for them
            if (topicsWithCognitiveData > 0 && !actualCognitiveCounts.isEmpty()) {
                cognitiveDistFitness = calculateDistributionFitness(
                        actualCognitiveCounts, cognitiveLevelDistributionTarget, totalSelectedTopics);
                        // Note: totalSelectedTopics is used as the denominator, which might need review
                        // if only a subset of topics have cognitive data.
                        // Alternative: use topicsWithCognitiveData if targetDist only covers known levels.
            } else {
                log.debug("Cognitive distribution fitness not calculated: no topics with cognitive data or actual counts map is empty, though target and weight were set.");
                // cognitiveDistFitness remains 1.0 (neutral)
            }
        } else {
            if (WEIGHT_COGNITIVE_DIST > 0.001) { // Log only if it was intended to be used
                 log.debug("Cognitive distribution fitness not calculated: Target distribution empty/null, or weight too small, or enhancement data missing.");
            }
            // cognitiveDistFitness remains 1.0 (neutral)
        }

        // --- 6. 计算知识点覆盖适应度 (Knowledge Point Coverage Fitness) ---
        // 衡量对目标知识点的覆盖程度。
        double kpCoverageFitness = calculateKnowledgePointCoverageFitness(
                selectedQuestions, targetKnowledgeIds);

        // --- 7. 计算题型多样性适应度 (Topic Type Diversity Fitness) ---
        // 衡量所选题目对不同题型的覆盖程度
        double typeDistributionFitness = calculateTopicTypeDistributionFitness(selectedQuestions);

        // --- 8. 计算标签多样性适应度 (Tag Diversity Fitness) ---
        double tagDiversityFitness = calculateTagDiversityFitness(selectedQuestions);

        // --- 9.  计算知识点题型均衡适应度 (Knowledge Point Type Balance Fitness) ---
        // 衡量每个知识点内部题型分布的均匀性，避免某个知识点某题型过多而某题型过少
        double kpTypeBalanceFitness = calculateKnowledgePointTypeBalanceFitness(selectedQuestions, targetKnowledgeIds);

        // --- 10. 计算最终总适应度 (Weighted Sum) ---
        // 各子适应度根据预设权重加权求和。
        double finalFitness = (WEIGHT_SCORE * scoreFitness) +
                              (WEIGHT_QUALITY * averageQuality) + // 使用 averageQuality 作为 qualityFitness
                              (WEIGHT_DIFFICULTY_DIST * difficultyDistFitness) +
                              (WEIGHT_COGNITIVE_DIST * cognitiveDistFitness) +
                              (WEIGHT_KP_COVERAGE * kpCoverageFitness) +
                              (WEIGHT_TOPIC_TYPE_DIVERSITY * typeDistributionFitness) + // 添加题型多样性适应度
                              (WEIGHT_TAG_DIVERSITY * tagDiversityFitness) + // 新增标签多样性适应度
                              (WEIGHT_KP_TYPE_BALANCE * kpTypeBalanceFitness); //  添加知识点题型均衡适应度

        chromosome.setFitness(finalFitness);
    }

    /**
     * 辅助方法：将数值型的题目难度值映射为预定义的难度名称（如 "easy", "medium", "hard"）。
     * 用于与基于名称的目标难度分布进行比较。
     *
     * @param difficultyValue 数值型的难度，通常是一个浮点数。
     * @return 对应的难度名称字符串。
     */
    /**
     * 根据数值难度映射到 "easy"|"medium"|"hard"。
     * 难度区间定义：
     * - 简单题：0.1-0.4 (algorithm.genetic.difficulty.easy-max)
     * - 中等题：0.5-0.7 (algorithm.genetic.difficulty.medium-min ~ medium-max)
     * - 难题：0.8-1.0 (algorithm.genetic.difficulty.hard-min ~ 1.0)
     * - 间隙题目：0.4-0.5和0.7-0.8之间的题目将被归类为最接近的类别
     */
    private String getDifficultyName(double difficultyValue) {
        // 简单题：<= 0.4
        if (difficultyValue <= DIFF_EASY_MAX) {
            return "easy";
        }
        // 难题：>= 0.8
        if (difficultyValue >= DIFF_HARD_MIN) {
            return "hard";
        }
        // 中等题：0.5-0.7，以及间隙区间的题目
        if (difficultyValue >= DIFF_MEDIUM_MIN && difficultyValue <= DIFF_MEDIUM_MAX) {
            return "medium";
        }

        // 处理间隙区间：0.4-0.5 和 0.7-0.8
        if (difficultyValue > DIFF_EASY_MAX && difficultyValue < DIFF_MEDIUM_MIN) {
            // 0.4-0.5区间，归类为更接近的类别
            double distanceToEasy = difficultyValue - DIFF_EASY_MAX;
            double distanceToMedium = DIFF_MEDIUM_MIN - difficultyValue;
            return distanceToEasy <= distanceToMedium ? "easy" : "medium";
        }

        if (difficultyValue > DIFF_MEDIUM_MAX && difficultyValue < DIFF_HARD_MIN) {
            // 0.7-0.8区间，归类为更接近的类别
            double distanceToMedium = difficultyValue - DIFF_MEDIUM_MAX;
            double distanceToHard = DIFF_HARD_MIN - difficultyValue;
            return distanceToMedium <= distanceToHard ? "medium" : "hard";
        }

        // 默认情况（不应该到达这里）
        log.warn("Unexpected difficulty value: {}. Defaulting to medium.", difficultyValue);
        return "medium";
    }

    /**
     * 初始化种群。随机生成一组染色体（代表可能的题目组合）。
     * 每个染色体的基因 (BitSet) 代表对候选题目列表的选择，1表示选中，0表示未选中。
     *
     * @param geneLength 基因序列的长度，等于候选题目列表的大小。
     * @return 生成的初始染色体种群列表。
     */
    /**
     * 计算标签多样性适应度：uniqueTagCount / totalSelectedTopics，范围 0-1。
     */
    private double calculateTagDiversityFitness(List<Topic> selectedQuestions) {
        if (selectedQuestions == null || selectedQuestions.isEmpty()) {
            return 0;
        }
        java.util.Set<String> uniqueTags = new java.util.HashSet<>();
        for (Topic q : selectedQuestions) {
            String tagStr = q.getTags();
            if (tagStr != null && !tagStr.trim().isEmpty()) {
                String[] arr = tagStr.split(",");
                for (String t : arr) {
                    String trimmed = t.trim();
                    if (!trimmed.isEmpty()) {
                        uniqueTags.add(trimmed);
                    }
                }
            }
        }
        int total = selectedQuestions.size();
        if (total == 0) return 0;
        double ratio = uniqueTags.size() / (double) total;
        // 以 ratio 作为适应度，确保 0-1 之间
        return Math.min(1.0, ratio);
    }

    private List<Chromosome> initializePopulation(int geneLength) {
        List<Chromosome> population = new ArrayList<>(POPULATION_SIZE);
        Random random = getThreadSafeRandom(); // 使用线程安全的随机数生成器

        for (int i = 0; i < POPULATION_SIZE; i++) {
            BitSet gene = new BitSet(geneLength);
            for (int j = 0; j < geneLength; j++) {
                // 以约50%的概率决定是否选择该题目，可以根据实际情况调整此初始选择概率
                if (random.nextDouble() < 0.5) {
                    gene.set(j);
                }
            }
            population.add(new Chromosome(gene));
        }
        return population;
    }

    /**
     * 锦标赛选择策略。
     * 从当前种群中随机选取 {@link #TOURNAMENT_SIZE} 个个体进行"锦标赛"，
     * 其中适应度最高的个体被选中作为父代。
     *
     * @param population 当前染色体种群。
     * @return 经过锦标赛选择胜出的染色体。
     */
    private Chromosome tournamentSelection(List<Chromosome> population) {
        if (population == null || population.isEmpty()) {
            throw new IllegalArgumentException("Population cannot be null or empty for tournament selection.");
        }
        List<Chromosome> tournamentCandidates = new ArrayList<>(TOURNAMENT_SIZE);
        Random random = getThreadSafeRandom();

        for (int i = 0; i < TOURNAMENT_SIZE; i++) {
            // 随机从种群中选择一个候选者（允许重复选择，即放回抽样）
            tournamentCandidates.add(population.get(random.nextInt(population.size())));
        }

        // 从锦标赛候选者中选出适应度最高的那个
        return Collections.max(tournamentCandidates, Comparator.comparing(Chromosome::getFitness));
    }

    /**
     * 类型保持交叉操作。
     * 此交叉操作确保子代染色体维持与父代相同的题型分布。
     *
     * @param parent1 父代染色体1
     * @param parent2 父代染色体2
     * @param questions 候选题目列表
     * @param typeTargetCounts 目标题型数量
     * @return 生成的子代染色体
     */
    private Chromosome typePreservingCrossover(Chromosome parent1, Chromosome parent2,
                                               List<Topic> questions,
                                               Map<String, Integer> typeTargetCounts) {
        if (parent1 == null || parent2 == null) {
            return new Chromosome(new BitSet());
        }

        Random random = getThreadSafeRandom();
        if (random.nextDouble() > CROSSOVER_RATE) {
            return new Chromosome((BitSet) (random.nextBoolean() ? parent1.getGene().clone() : parent2.getGene().clone()));
        }

        // 检查父代是否都满足类型约束
        Map<String, Set<Integer>> typeIndicesParent1 = getTypeIndices(parent1.getGene(), questions);
        Map<String, Set<Integer>> typeIndicesParent2 = getTypeIndices(parent2.getGene(), questions);

        // 创建子代基因
        BitSet childGene = new BitSet(questions.size());

        // 对每种题型分别进行交叉
        for (String type : typeTargetCounts.keySet()) {
            int targetCount = typeTargetCounts.getOrDefault(type, 0);
            if (targetCount <= 0) continue;

            Set<Integer> indicesOfTypeParent1 = typeIndicesParent1.getOrDefault(type, new HashSet<>());
            Set<Integer> indicesOfTypeParent2 = typeIndicesParent2.getOrDefault(type, new HashSet<>());

            // 如果任一父代没有足够的此类型题目，则联合两个父代的题目
            Set<Integer> allAvailableIndicesOfType = new HashSet<>(indicesOfTypeParent1);
            allAvailableIndicesOfType.addAll(indicesOfTypeParent2);

            if (allAvailableIndicesOfType.size() < targetCount) {
                // 题目不足，全部选择
                for (int index : allAvailableIndicesOfType) {
                    childGene.set(index);
                }
            } else {
                // 随机选择交叉点 - 对此类型的索引列表进行切分
                List<Integer> parent1IndicesList = new ArrayList<>(indicesOfTypeParent1);
                List<Integer> parent2IndicesList = new ArrayList<>(indicesOfTypeParent2);

                if (!parent1IndicesList.isEmpty() && !parent2IndicesList.isEmpty()) {
                    // 对索引列表排序，以便确定性地进行交叉
                    Collections.sort(parent1IndicesList);
                    Collections.sort(parent2IndicesList);

                    // 随机选择交叉点，范围在0和目标数量之间
                    int crossPoint = random.nextInt(targetCount + 1);

                    // 从第一个父代选择前crossPoint个元素
                    for (int i = 0; i < Math.min(crossPoint, parent1IndicesList.size()); i++) {
                        childGene.set(parent1IndicesList.get(i));
                    }

                    // 从第二个父代选择剩余元素，直到达到目标数量
                    int remaining = targetCount - Math.min(crossPoint, parent1IndicesList.size());
                    for (int i = 0; i < Math.min(remaining, parent2IndicesList.size()); i++) {
                        childGene.set(parent2IndicesList.get(i));
                    }

                    // 如果仍然不足，从第一个父代的剩余元素中补充
                    if (childGene.cardinality() < targetCount) {
                        int stillNeeded = targetCount - childGene.cardinality();
                        for (int i = crossPoint; i < parent1IndicesList.size() && stillNeeded > 0; i++) {
                            int index = parent1IndicesList.get(i);
                            if (!childGene.get(index)) {
                                childGene.set(index);
                                stillNeeded--;
                            }
                        }
                    }

                    // 如果仍然不足，从第二个父代的剩余元素中补充
                    if (childGene.cardinality() < targetCount) {
                        int stillNeeded = targetCount - childGene.cardinality();
                        for (int i = remaining; i < parent2IndicesList.size() && stillNeeded > 0; i++) {
                            int index = parent2IndicesList.get(i);
                            if (!childGene.get(index)) {
                                childGene.set(index);
                                stillNeeded--;
                            }
                        }
                    }
                } else if (!parent1IndicesList.isEmpty()) {
                    // 只有第一个父代有此类型，尽可能选择足够数量
                    for (int i = 0; i < Math.min(targetCount, parent1IndicesList.size()); i++) {
                        childGene.set(parent1IndicesList.get(i));
                    }
                } else if (!parent2IndicesList.isEmpty()) {
                    // 只有第二个父代有此类型，尽可能选择足够数量
                    for (int i = 0; i < Math.min(targetCount, parent2IndicesList.size()); i++) {
                        childGene.set(parent2IndicesList.get(i));
                    }
                }
            }
        }

        return new Chromosome(childGene);
    }

    /**
     * 类型保持变异操作。
     * 此变异操作确保变异后的染色体维持所需的题型数量分布。
     *
     * @param chromosome 待变异的染色体
     * @param questions 候选题目列表
     * @param typeTargetCounts 目标题型数量
     */
    private void typePreservingMutate(Chromosome chromosome, List<Topic> questions, Map<String, Integer> typeTargetCounts) {
        BitSet gene = chromosome.getGene();
        if (gene.length() == 0) return;

        Random random = getThreadSafeRandom();

        // 获取当前染色体按题型分组的索引
        Map<String, Set<Integer>> typeIndices = getTypeIndices(gene, questions);

        // 对每种题型单独变异，确保变异后保持相同数量
        for (String type : typeTargetCounts.keySet()) {
            int targetCount = typeTargetCounts.getOrDefault(type, 0);
            if (targetCount <= 0) continue;

            Set<Integer> indicesOfType = typeIndices.getOrDefault(type, new HashSet<>());
            int currentCount = indicesOfType.size();

            // 如果当前数量等于目标数量，执行1对1替换变异
            if (currentCount == targetCount && random.nextDouble() < MUTATION_RATE) {
                // 查找此类型的所有候选题目索引
                Set<Integer> allCandidateIndicesOfType = getAllIndicesOfType(questions, type);

                // 移除当前已选索引，得到可以替换的候选集
                Set<Integer> candidatesForReplacement = new HashSet<>(allCandidateIndicesOfType);
                candidatesForReplacement.removeAll(indicesOfType);

                if (!candidatesForReplacement.isEmpty() && !indicesOfType.isEmpty()) {
                    // 随机选择一个当前索引移除
                    List<Integer> currentIndicesList = new ArrayList<>(indicesOfType);
                    int indexToRemove = currentIndicesList.get(random.nextInt(currentIndicesList.size()));

                    // 随机选择一个新索引添加
                    List<Integer> candidatesList = new ArrayList<>(candidatesForReplacement);
                    int indexToAdd = candidatesList.get(random.nextInt(candidatesList.size()));

                    // 执行替换
                    gene.clear(indexToRemove);
                    gene.set(indexToAdd);
                }
            }
            // 如果当前数量小于目标数量，尝试添加新题目
            else if (currentCount < targetCount) {
                // 查找此类型的所有可添加候选题目
                Set<Integer> allCandidateIndicesOfType = getAllIndicesOfType(questions, type);
                Set<Integer> candidatesForAddition = new HashSet<>(allCandidateIndicesOfType);
                candidatesForAddition.removeAll(indicesOfType);

                if (!candidatesForAddition.isEmpty()) {
                    // 随机选择并添加所需数量的题目
                    List<Integer> candidatesList = new ArrayList<>(candidatesForAddition);
                    Collections.shuffle(candidatesList, random);

                    for (int i = 0; i < Math.min(targetCount - currentCount, candidatesList.size()); i++) {
                        gene.set(candidatesList.get(i));
                    }
                }
            }
            // 如果当前数量大于目标数量，移除多余题目
            else if (currentCount > targetCount) {
                // 随机选择并移除多余题目
                List<Integer> currentIndicesList = new ArrayList<>(indicesOfType);
                Collections.shuffle(currentIndicesList, random);

                for (int i = targetCount; i < currentCount && i < currentIndicesList.size(); i++) {
                    gene.clear(currentIndicesList.get(i));
                }
            }
        }
    }

    /**
     * 获取指定题型的所有候选题目索引
     */
    private Set<Integer> getAllIndicesOfType(List<Topic> questions, String type) {
        Set<Integer> indices = new HashSet<>();
        for (int i = 0; i < questions.size(); i++) {
            if (getStandardTopicType(questions.get(i).getType()).equals(type)) {
                indices.add(i);
            }
        }
        return indices;
    }

    /**
     * 获取染色体中按题型分组的索引
     */
    private Map<String, Set<Integer>> getTypeIndices(BitSet gene, List<Topic> questions) {
        Map<String, Set<Integer>> typeIndices = new HashMap<>();

        for (int i = 0; i < questions.size(); i++) {
            if (gene.get(i)) {
                String type = getStandardTopicType(questions.get(i).getType());
                typeIndices.computeIfAbsent(type, k -> new HashSet<>()).add(i);
            }
        }

        return typeIndices;
    }

    /**
     * 从最优染色体中提取实际的题目列表。
     * 根据染色体的基因序列（BitSet），从原始可用题目列表中筛选出被选中的题目。
     *
     * @param bestSolutionChromosome 适应度最高的染色体。
     * @param questions              原始可用题目列表。
     * @return 染色体代表的题目组合（一个 {@link Topic} 列表）。
     */
    private List<Topic> extractSolution(Chromosome bestSolutionChromosome, List<Topic> questions) {
        List<Topic> solutionTopics = new ArrayList<>();
        if (bestSolutionChromosome == null) return solutionTopics;

        BitSet gene = bestSolutionChromosome.getGene();
        for (int i = 0; i < questions.size(); i++) { // 遍历候选题目列表
            if (i < gene.length() && gene.get(i)) { // 如果基因位为1且在基因长度内
                solutionTopics.add(questions.get(i));
            }
        }
        return solutionTopics;
    }

    /**
     * (未使用) 计算种群多样性的一个示例方法。
     * 可以通过计算种群中个体间基因的平均汉明距离等指标来衡量多样性。
     * 高多样性有助于算法探索更广阔的解空间，避免早熟。
     *
     * @param population 染色体种群。
     * @return 种群多样性的一个量化指标。
     */
    @SuppressWarnings("unused") // 标记为未使用，但保留作为未来扩展或参考
    private double calculateDiversity(List<Chromosome> population) {
        if (population == null || population.isEmpty() || population.size() < 2) return 0.0;

        double totalPairwiseDistance = 0.0;
        int comparisons = 0;

        // 为避免 N^2 复杂度，可以对部分样本进行比较，例如随机选取或只比较部分精英个体
        int sampleSize = Math.min(population.size(), 30); // 例如，最多取30个样本进行比较
        List<Chromosome> samplePopulation = new ArrayList<>(population.subList(0, sampleSize));

        for (int i = 0; i < samplePopulation.size(); i++) {
            for (int j = i + 1; j < samplePopulation.size(); j++) {
                totalPairwiseDistance += hammingDistance(
                    samplePopulation.get(i).getGene(),
                    samplePopulation.get(j).getGene()
                );
                comparisons++;
            }
        }
        return comparisons > 0 ? totalPairwiseDistance / comparisons : 0.0;
    }

    /**
     * (未使用) 计算两个基因序列（BitSet）之间的归一化汉明距离。
     * 汉明距离指两个等长字符串对应位置的不同字符的个数。
     * 此处归一化到 [0,1] 区间，表示差异程度。
     *
     * @param gene1 第一个基因序列。
     * @param gene2 第二个基因序列。
     * @return 归一化的汉明距离。
     */
    private double hammingDistance(BitSet gene1, BitSet gene2) {
        BitSet xorResult = (BitSet) gene1.clone();
        xorResult.xor(gene2); // 执行异或操作，结果中为1的位表示对应位置不同

        // 基因长度取两者中较大者，以正确处理不等长BitSet的情况 (尽管GA中通常等长)
        int effectiveLength = Math.max(gene1.length(), gene2.length());
        if (effectiveLength == 0) return 0.0;

        // xorResult.cardinality() 返回BitSet中值为1的位的数量，即不同位的数量
        return (double) xorResult.cardinality() / effectiveLength;
    }

    /**
     * 内部类，代表遗传算法中的染色体（一个个体或一个潜在解）。
     * 包含基因序列 (BitSet) 和该染色体的适应度值及总分。
     * 线程安全版本，使用同步机制保护共享状态。
     */
    public static class Chromosome {
        /** 基因序列，使用 BitSet 表示，每一位对应候选题目列表中的一个题目是否被选中。 */
        private final BitSet gene;

        /** 该染色体的适应度值，综合评价其优劣。 */
        private volatile double fitness;

        /** 该染色体所代表的题目组合的总分。 */
        private volatile int totalScore;

        /** 同步锁，保护基因序列的访问 */
        private final Object geneLock = new Object();

        /**
         * Chromosome 构造函数。
         * @param gene 该染色体的基因序列。
         */
        public Chromosome(BitSet gene) {
            synchronized (geneLock) {
                this.gene = (BitSet) gene.clone(); // 防御性复制
            }
            this.fitness = 0.0; // 初始适应度为0
            this.totalScore = 0;  // 初始总分为0
        }

        /**
         * 线程安全地获取基因序列的副本
         */
        public BitSet getGene() {
            synchronized (geneLock) {
                return (BitSet) gene.clone();
            }
        }

        /**
         * 获取适应度值
         */
        public double getFitness() {
            return fitness;
        }

        /**
         * 设置适应度值
         */
        public void setFitness(double fitness) {
            this.fitness = fitness;
        }

        /**
         * 获取总分
         */
        public int getTotalScore() {
            return totalScore;
        }

        /**
         * 设置总分
         */
        public void setTotalScore(int totalScore) {
            this.totalScore = totalScore;
        }
    }

    /**
     * 计算基于数值的难度分布适应度。
     * 首先将数值型的实际难度计数转换为类别型（easy, medium, hard）的计数，
     * 然后调用通用的 {@link #calculateDistributionFitness} 方法计算适应度。
     *
     * @param actualNumericDifficultyCounts Map<难度值, 数量>，表示实际选出题目中各数值难度的题目数量。
     * @param targetDifficultyDistribution Map<难度名, 期望百分比>，目标难度分布。
     * @param totalSelectedTopics 实际选中的总题目数。
     * @return 难度分布适应度。
     */
    private double calculateNumericDistributionFitness(Map<Double, Integer> actualNumericDifficultyCounts,
                                               Map<String, Double> targetDifficultyDistribution,
                                               int totalSelectedTopics) {
        if (targetDifficultyDistribution == null || targetDifficultyDistribution.isEmpty() || totalSelectedTopics == 0) {
            return 1.0; // 没有目标分布或没有选中题目，视为完美匹配
        }

        // 将实际的数值难度计数，按 getDifficultyName 规则，转换为按 "easy", "medium", "hard" 分类的计数
        Map<String, Integer> categorizedActualCounts = new HashMap<>();
        for (Map.Entry<Double, Integer> entry : actualNumericDifficultyCounts.entrySet()) {
            String difficultyName = getDifficultyName(entry.getKey()); // 将0.1, 0.3, 0.5等映射为"easy", "medium", "hard"
            categorizedActualCounts.put(difficultyName,
                    categorizedActualCounts.getOrDefault(difficultyName, 0) + entry.getValue());
        }

        // 使用转换后的类别计数，调用通用的分布适应度计算方法
        return calculateDistributionFitness(categorizedActualCounts, targetDifficultyDistribution, totalSelectedTopics);
    }

    /**
     * (空方法，仅为演示或未来扩展保留) 性能基准测试方法。
     * 可用于评估不同参数配置下遗传算法的执行效率。
     */
    @SuppressWarnings("unused")
    public void benchmarkPaperGeneration() {
        // 此处可实现性能基准测试的相关逻辑，例如：
        // 1. 构建固定的测试用例 (availableQuestions, targetScore等)
        // 2. 多次调用 solve 方法并记录执行时间
        // 3. 分析不同参数（种群大小、迭代次数等）对性能和结果质量的影响
        log.info("Benchmark method called. Implement benchmark logic here.");
    }

    /**
     * 计算题型多样性适应度。
     * 衡量所选题目对不同题型的覆盖程度，优先保证题型平衡分布。
     *
     * @param selectedTopics 当前染色体选中的题目列表
     * @return 题型多样性适应度，值域在 [0, 1]，1表示所有题型都已覆盖
     */
    private double calculateTopicTypeDistributionFitness(List<Topic> selectedTopics) {
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return 0.0; // 如果没有选中题目，适应度为0
        }

        // 要重点关注的核心题型，这些题型在适应度计算中权重更高 - 使用数据库标准格式
        Set<String> coreTypes = new HashSet<>(Arrays.asList(
            TopicTypeMapper.DB_SINGLE_CHOICE,
            TopicTypeMapper.DB_MULTIPLE_CHOICE,
            TopicTypeMapper.DB_JUDGMENT,
            TopicTypeMapper.DB_FILL_BLANK,
            TopicTypeMapper.DB_SHORT_ANSWER
        ));

        // 统计当前染色体中各题型的题目数量
        Map<String, Integer> typeCountMap = new HashMap<>();
        for (Topic topic : selectedTopics) {
            // 确保题型名称的标准化
            String standardType = getStandardTopicType(topic.getType());
            typeCountMap.put(standardType, typeCountMap.getOrDefault(standardType, 0) + 1);
        }

        // 计算题型覆盖率
        double coveredCoreTypes = 0;
        double totalCoreTypes = coreTypes.size();

        for (String coreType : coreTypes) {
            if (typeCountMap.containsKey(coreType) && typeCountMap.get(coreType) > 0) {
                coveredCoreTypes++;
            }
        }

        // 基础适应度：核心题型的覆盖比例
        double baseFitness = totalCoreTypes > 0 ? coveredCoreTypes / totalCoreTypes : 0;

        // 计算各题型分布的均衡性，防止某一题型数量过多或过少
        double balancePenalty = 0;
        if (baseFitness > 0) {
            // 计算平均每种题型应有的题目数量
            double avgItemsPerType = (double) selectedTopics.size() / coveredCoreTypes;

            // 计算实际题型分布与理想均衡分布的偏差
            double totalDeviation = 0;
            int countedTypes = 0;
            for (String type : coreTypes) {
                if (typeCountMap.containsKey(type) && typeCountMap.get(type) > 0) {
                    int count = typeCountMap.get(type);
                    // 计算与平均值的差异的绝对值
                    double deviation = Math.abs(count - avgItemsPerType) / avgItemsPerType;
                    totalDeviation += deviation;
                    countedTypes++;
                }
            }

            // 平均偏差率，范围 [0, 无穷)，但实际上通常小于2.0
            double avgDeviation = countedTypes > 0 ? totalDeviation / countedTypes : 0;

            // 弄一个减少函数，区间 [0, 1]，如果偏差为0，则为1；偏差越大，取值越小
            balancePenalty = Math.min(1.0, avgDeviation / 2.0); // 限制其最大值为1.0
        }

        // 最终适应度 = 基础覆盖适应度 * (1 - 不平衡惩罚)
        double finalFitness = baseFitness * (1.0 - balancePenalty);

        return finalFitness;
    }

    /**
     * 将各种题型标识映射为标准的数据库格式题型标识。
     * 使用统一的TopicTypeMapper工具类确保一致性。
     *
     * @param typeKey 原始题型标识
     * @return 数据库标准格式的题型标识
     */
    private String getStandardTopicType(String typeKey) {
        if (typeKey == null) {
            return TopicTypeMapper.DB_SINGLE_CHOICE; // 默认返回单选题
        }

        // 使用统一的映射工具，转换为数据库标准格式
        return TopicTypeMapper.toDbFormat(typeKey);
    }

    /**
     * 计算知识点覆盖适应度。
     * 衡量当前选中的题目集合对目标知识点列表的覆盖程度。
     *
     * @param selectedTopics 当前染色体（解方案）选中的题目列表。
     * @param targetKnowledgeIds 试卷要求覆盖的目标知识点ID列表。
     * @return 知识点覆盖适应度。值域通常在 [0, 1]，1表示完美覆盖。
     *         当前实现：如果未指定目标知识点，则为1；如果未选出题目，则为0。
     *         若目标知识点未被全部覆盖，则适应度会根据覆盖比例打折（例如乘以0.5）。
     */
    private double calculateKnowledgePointCoverageFitness(
            List<Topic> selectedTopics,
            List<Integer> targetKnowledgeIds) {

        if (targetKnowledgeIds == null || targetKnowledgeIds.isEmpty()) {
            return 1.0; // 没有指定知识点目标，默认完美覆盖 (或不参与此项评估)
        }
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return 0.0; // 没有选出任何题目，知识点覆盖度为0
        }

        Set<Integer> coveredKnowledgeIdsBySelection = new HashSet<>();
        // 可选：更细致的评估，例如每个目标知识点下题目的数量或总分
        // Map<Integer, Integer> topicsPerCoveredKp = new HashMap<>();
        // Map<Integer, Integer> scorePerCoveredKp = new HashMap<>();

        for (Topic topic : selectedTopics) {
            Integer knowId = topic.getKnowId(); // 假设 Topic 实体中有 getKnowId() 方法返回其所属知识点ID
            if (knowId != null && targetKnowledgeIds.contains(knowId)) {
                coveredKnowledgeIdsBySelection.add(knowId);
                // topicsPerCoveredKp.put(knowId, topicsPerCoveredKp.getOrDefault(knowId, 0) + 1);
                // scorePerCoveredKp.put(knowId, scorePerCoveredKp.getOrDefault(knowId, 0) + topic.getScore());
            }
        }

        // 计算覆盖到的目标知识点占所有目标知识点的比例
        double coverageRatio = (double) coveredKnowledgeIdsBySelection.size() / targetKnowledgeIds.size();

        // 策略：如果未能覆盖所有目标知识点，则给予惩罚
        // 例如，如果只覆盖了一半，那么适应度可能是 0.5 * (惩罚因子，如0.5) = 0.25
        // 如果全部覆盖，则适应度为1.0
        if (coveredKnowledgeIdsBySelection.size() < targetKnowledgeIds.size()) {
            return coverageRatio * 0.5; // 示例惩罚：未全覆盖则适应度减半 (基于覆盖比例)
        }

        // (未来可扩展) 如果所有目标知识点都已覆盖，可以进一步评估其均衡性
        // 例如，计算各覆盖知识点下题目数量或分数的标准差，标准差越小，均衡性越好，适应度越高。
        // double balanceFitness = calculateBalanceFitness(topicsPerCoveredKp, scorePerCoveredKp);
        // return balanceFitness; // (如果均衡性很重要)

        return coverageRatio; // 当前：如果全覆盖则为1.0，否则为打折后的覆盖比例
    }

    /**
     *  计算知识点题型均衡适应度
     * 衡量每个知识点内部题型分布的均匀性，避免某个知识点某题型过多而某题型过少
     *
     * @param selectedTopics 选中的题目列表
     * @param targetKnowledgeIds 目标知识点ID列表
     * @return 知识点题型均衡适应度，范围[0,1]，1表示完美均衡
     */
    private double calculateKnowledgePointTypeBalanceFitness(List<Topic> selectedTopics, List<Integer> targetKnowledgeIds) {
        if (selectedTopics == null || selectedTopics.isEmpty() || targetKnowledgeIds == null || targetKnowledgeIds.isEmpty()) {
            return 1.0; // 没有题目或知识点，默认完美均衡
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = new HashMap<>();
        for (Topic topic : selectedTopics) {
            Integer knowId = topic.getKnowId();
            if (knowId != null && targetKnowledgeIds.contains(knowId)) {
                topicsByKnowledgePoint.computeIfAbsent(knowId, k -> new ArrayList<>()).add(topic);
            }
        }

        if (topicsByKnowledgePoint.isEmpty()) {
            return 0.0; // 没有匹配的知识点，均衡度为0
        }

        double totalBalanceScore = 0.0;
        int validKnowledgePoints = 0;

        // 为每个知识点计算题型均衡度
        for (Map.Entry<Integer, List<Topic>> entry : topicsByKnowledgePoint.entrySet()) {
            Integer knowledgeId = entry.getKey();
            List<Topic> kpTopics = entry.getValue();

            if (kpTopics.size() < 2) {
                // 如果知识点只有1道题，认为是完美均衡（无法不均衡）
                totalBalanceScore += 1.0;
                validKnowledgePoints++;
                continue;
            }

            // 统计该知识点的题型分布
            Map<String, Integer> typeDistribution = new HashMap<>();
            for (Topic topic : kpTopics) {
                String type = getStandardTopicType(topic.getType());
                typeDistribution.put(type, typeDistribution.getOrDefault(type, 0) + 1);
            }

            // 计算该知识点的题型均衡度
            double kpBalanceScore = calculateTypeBalanceScore(typeDistribution, kpTopics.size());
            totalBalanceScore += kpBalanceScore;
            validKnowledgePoints++;

            log.debug("Knowledge point {} type balance: {} (types: {})",
                     knowledgeId, kpBalanceScore, typeDistribution);
        }

        // 返回所有知识点的平均均衡度
        double averageBalance = validKnowledgePoints > 0 ? totalBalanceScore / validKnowledgePoints : 0.0;

        log.debug("Overall knowledge point type balance fitness: {} (valid KPs: {})",
                 averageBalance, validKnowledgePoints);

        return averageBalance;
    }

    /**
     * 计算单个知识点的题型均衡分数
     *
     * @param typeDistribution 题型分布 Map<题型, 数量>
     * @param totalTopics 总题目数
     * @return 均衡分数，范围[0,1]，1表示完美均衡
     */
    private double calculateTypeBalanceScore(Map<String, Integer> typeDistribution, int totalTopics) {
        if (typeDistribution.isEmpty() || totalTopics == 0) {
            return 1.0; // 没有题型分布，默认完美
        }

        if (typeDistribution.size() == 1) {
            return 1.0; // 只有一种题型，无法不均衡
        }

        // 计算理想的平均每种题型数量
        double idealAverage = (double) totalTopics / typeDistribution.size();

        // 计算各题型与理想平均值的偏差
        double totalDeviation = 0.0;
        for (Integer count : typeDistribution.values()) {
            double deviation = Math.abs(count - idealAverage) / idealAverage;
            totalDeviation += deviation;
        }

        // 平均偏差率
        double averageDeviation = totalDeviation / typeDistribution.size();

        // 转换为均衡分数：偏差越小，分数越高
        // 使用指数衰减函数，确保分数在[0,1]范围内
        double balanceScore = Math.exp(-averageDeviation);

        return Math.max(0.0, Math.min(1.0, balanceScore));
    }

    /**
     * 计算自适应变异率
     * 根据当前代数和停滞代数动态调整变异率
     */
    private double calculateAdaptiveMutationRate(int generation, int generationsWithoutImprovement) {
        if (!ADAPTIVE_MUTATION_ENABLED) {
            // 如果未启用自适应变异，使用传统的线性递减
            return MUTATION_RATE * Math.max(0.1, (1.0 - (double)generation / MAX_GENERATIONS * 0.9));
        }

        // 基础变异率：随进化进程线性递减
        double baseRate = ADAPTIVE_MUTATION_MIN_RATE +
                         (ADAPTIVE_MUTATION_MAX_RATE - ADAPTIVE_MUTATION_MIN_RATE) *
                         (1.0 - (double)generation / MAX_GENERATIONS);

        // 停滞惩罚：如果连续多代无改进，提高变异率
        if (generationsWithoutImprovement >= ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) {
            double stagnationMultiplier = 1.0 +
                (generationsWithoutImprovement - ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) * 0.1;
            baseRate = Math.min(ADAPTIVE_MUTATION_MAX_RATE, baseRate * stagnationMultiplier);
        }

        return Math.max(ADAPTIVE_MUTATION_MIN_RATE, Math.min(ADAPTIVE_MUTATION_MAX_RATE, baseRate));
    }

    /**
     * 将数据库格式的题型转换为前端格式
     * 使用统一的TopicTypeMapper工具类确保一致性
     */
    private String convertDbTypeToFrontendType(String dbType) {
        if (dbType == null) return TopicTypeMapper.FRONTEND_SINGLE_CHOICE;

        // 使用统一的TopicTypeMapper工具类进行转换
        return TopicTypeMapper.toFrontendFormat(dbType);
    }

    /**
     * 从染色体中提取选中的题目列表
     */
    private List<Topic> getSelectedTopics(Chromosome chromosome, List<Topic> availableQuestions) {
        List<Topic> selectedTopics = new ArrayList<>();
        BitSet gene = chromosome.getGene();

        for (int i = 0; i < gene.length() && i < availableQuestions.size(); i++) {
            if (gene.get(i)) {
                selectedTopics.add(availableQuestions.get(i));
            }
        }

        return selectedTopics;
    }
}